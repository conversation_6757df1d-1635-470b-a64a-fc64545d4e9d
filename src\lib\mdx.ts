import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { marked } from 'marked'
import hljs from 'highlight.js'

const postsDirectory = path.join(process.cwd(), 'content/blog')

// Configure marked
marked.setOptions({
  breaks: true,
  gfm: true,
})

// Custom renderer for syntax highlighting
const renderer = new marked.Renderer()

renderer.code = function(code: string, language?: string): string {
  if (language && hljs.getLanguage(language)) {
    try {
      const highlighted = hljs.highlight(code, { language }).value
      return `<pre><code class="hljs language-${language}">${highlighted}</code></pre>`
    } catch (err) {
      // Fall back to auto-detection
    }
  }

  // Auto-detect language if none specified
  try {
    const highlighted = hljs.highlightAuto(code).value
    return `<pre><code class="hljs">${highlighted}</code></pre>`
  } catch (err) {
    // Final fallback to plain code
    return `<pre><code>${code}</code></pre>`
  }
}

marked.use({ renderer })

export interface BlogPost {
  slug: string
  title: string
  excerpt: string
  date: string
  category: string
  readTime: string
  author: string
  featured: boolean
  content: string
  seo: {
    title: string
    description: string
    keywords: string[]
  }
}

export interface BlogPostMeta extends Omit<BlogPost, 'content'> {}

export function getAllPosts(): BlogPostMeta[] {
  const fileNames = fs.readdirSync(postsDirectory)
  const allPostsData = fileNames
    .filter((fileName) => fileName.endsWith('.mdx'))
    .map((fileName) => {
      const slug = fileName.replace(/\.mdx$/, '')
      const fullPath = path.join(postsDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data } = matter(fileContents)

      return {
        slug,
        title: data.title,
        excerpt: data.excerpt,
        date: data.date,
        category: data.category,
        readTime: data.readTime,
        author: data.author,
        featured: data.featured || false,
        seo: data.seo,
      } as BlogPostMeta
    })

  // Sort posts by date
  return allPostsData.sort((a, b) => (a.date < b.date ? 1 : -1))
}

export function getPostBySlug(slug: string): BlogPost | null {
  try {
    const fullPath = path.join(postsDirectory, `${slug}.mdx`)
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)

    // Convert markdown to HTML
    const htmlContent = marked(content)

    return {
      slug,
      title: data.title,
      excerpt: data.excerpt,
      date: data.date,
      category: data.category,
      readTime: data.readTime,
      author: data.author,
      featured: data.featured || false,
      content: htmlContent,
      seo: data.seo,
    } as BlogPost
  } catch (error) {
    return null
  }
}

export function getFeaturedPosts(): BlogPostMeta[] {
  const allPosts = getAllPosts()
  return allPosts.filter((post) => post.featured)
}

export function getPostsByCategory(category: string): BlogPostMeta[] {
  const allPosts = getAllPosts()
  return allPosts.filter((post) => post.category === category)
}

export function getAllCategories(): string[] {
  const allPosts = getAllPosts()
  const categories = allPosts.map((post) => post.category)
  return Array.from(new Set(categories))
}

export function getRelatedPosts(currentSlug: string, category: string, limit: number = 3): BlogPostMeta[] {
  const allPosts = getAllPosts()
  const relatedPosts = allPosts
    .filter((post) => post.slug !== currentSlug && post.category === category)
    .slice(0, limit)
  
  return relatedPosts
}
